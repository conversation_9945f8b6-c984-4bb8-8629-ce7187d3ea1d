package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.model.CustomerBind;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 20:53
 * @description:
 */
public interface CustomerBindBuilder {

    String getTableName();

    boolean insert(CustomerBind customerBind);

    boolean delete(CustomerBind customerBind);

    CustomerBind findByCustomerId(String customerId, String socialPlatform);

    List<CustomerBind> findByCustomerId(String customerId);

    List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms);

    /**
     * 根据社交平台信息查找已绑定的客户记录
     * 支持按userId、userName、email等字段进行查询
     *
     * @param query 查询参数
     * @return 匹配的客户绑定记录，如果没有找到则返回null
     */
    CustomerBind findBySocialInfo(CustomerBindQuery query);

}
