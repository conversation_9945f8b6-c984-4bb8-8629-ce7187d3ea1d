package com.drex.customer.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/27 20:36
 * @description:
 */
@Data
@Table(name = Constant.TABLE_NAME_CUSTOMER_BIND)
public class CustomerBind implements Serializable {

    public static final String SEARCH_CUSTOMER_BIND = "search_customer_bind";
    public static final String IDX_CUSTOMER_BIND = "idx_customer_bind";


    @PartitionKey(name = "customer_id")
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "customer_id")
    @Index(name = IDX_CUSTOMER_BIND, pkColumn = "customer_id", pkValue = 2)
    private String customerId;

    @PartitionKey(name = "social_platform")
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "social_platform")
    @Index(name = IDX_CUSTOMER_BIND, pkColumn = "social_platform", pkValue = 1)
    private String socialPlatform;

    @Column(name = "social_user_id",isDefined = true)
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "social_user_id")
    @Index(name = IDX_CUSTOMER_BIND, pkColumn = "social_user_id")
    private String socialUserId;

    @Column(name = "social_handle_name",isDefined = true)
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "social_handle_name")
    private String socialHandleName;

    @Column(name = "social_profile_image",isDefined = true)
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "social_profile_image")
    private String socialProfileImage;

    @Column(name = "social_email",isDefined = true)
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "social_email")
    private String socialEmail;

    @Column(name = "privacy_auth",isDefined = true)
    @SearchIndex(name = SEARCH_CUSTOMER_BIND, column = "privacy_auth", fieldType = FieldType.BOOLEAN)
    private Boolean privacyAuth;

    @Column(name = "created")
    private Long created;

}
