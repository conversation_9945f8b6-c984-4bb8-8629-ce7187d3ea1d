package com.drex.customer.dal.tablestore.builder.impl;

import com.drex.customer.dal.tablestore.builder.CustomerWaitListBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/5/12 18:51
 * @description:
 */
@Repository
public class CustomerWaitListBuilderImpl extends WideColumnStoreBuilder<CustomerWaitList> implements CustomerWaitListBuilder {

    @Override
    public String getTableName() {
        return Constant.TABLE_NAME_CUSTOMER_WAIT_LIST;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerWaitList.class);
    }

    @Override
    public boolean insertOrUpdate(CustomerWaitList customerWaitList) {
        return super.putRow(customerWaitList);
    }
}
