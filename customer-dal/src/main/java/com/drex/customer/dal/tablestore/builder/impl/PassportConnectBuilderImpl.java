package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.drex.customer.dal.tablestore.builder.PassportConnectBuilder;
import com.drex.customer.dal.tablestore.model.PassportConnect;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PassportConnectBuilderImpl extends WideColumnStoreBuilder<PassportConnect> implements PassportConnectBuilder {

    @Override
    public String tableName() {
        return PassportConnect.TABLE_NAME;
    }

    @PostConstruct
    public void init() {
        super.init(PassportConnect.class);
    }

    @Override
    public PassportConnect getByIdentifier(String walletAddress, String subConnectProvider) {
        if (StringUtils.isBlank(walletAddress) || StringUtils.isBlank(subConnectProvider)) {
            return null;
        }
        walletAddress = walletAddress.toLowerCase();
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.fromString(walletAddress), PrimaryKeyValue.fromString(walletAddress)));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.fromString(subConnectProvider), PrimaryKeyValue.fromString(subConnectProvider)));
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryOne(parameterList);
    }

    @Override
    public List<PassportConnect> getByIdentifier(String walletAddress) {
        if (StringUtils.isBlank(walletAddress)) {
            return null;
        }
        walletAddress = walletAddress.toLowerCase();
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.fromString(walletAddress), PrimaryKeyValue.fromString(walletAddress)));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQuery(parameterList);
    }

    @Override
    public Boolean save(PassportConnect passportConnect) {
        passportConnect.setIdentifier(passportConnect.getIdentifier().toLowerCase());
        return putRow(passportConnect);
    }

    @Override
    public List<PassportConnect> getByPassportId(String passportId) {
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.fromString(passportId), PrimaryKeyValue.fromString(passportId)));
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQuery(PassportConnect.INDEX_IDENTIFIER, parameterList);
    }

    @Override
    public PassportConnect getByPassportIdAndWalletAddress(String passportId, String walletAddress) {
        List<RangeQueryParameter> parameterList = new ArrayList<>();
        parameterList.add(new RangeQueryParameter("passport_id", PrimaryKeyValue.fromString(passportId), PrimaryKeyValue.fromString(passportId)));
        parameterList.add(new RangeQueryParameter("identifier", PrimaryKeyValue.fromString(walletAddress), PrimaryKeyValue.fromString(walletAddress)));
        parameterList.add(new RangeQueryParameter("sub_connect_provider", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryOne(PassportConnect.INDEX_IDENTIFIER, parameterList);
    }

    @Override
    public Boolean updatePassportConnect(PassportConnect passportConnect) {
        return super.updateRow(passportConnect);
    }
}
