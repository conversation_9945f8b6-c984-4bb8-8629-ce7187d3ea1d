package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.MatchAllQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.customer.dal.tablestore.IdUtils;
import com.drex.customer.dal.tablestore.builder.PassportBuilder;
import com.drex.customer.dal.tablestore.model.Passport;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PassportBuilderImpl extends WideColumnStoreBuilder<Passport> implements PassportBuilder {

    @Autowired
    private IdUtils idUtils;

    @Override
    public String tableName() {
        return Passport.TABLE_NAME;
    }

    @PostConstruct
    public void init() {
        super.init(Passport.class);
    }

    @Override
    public Passport getByPassportId(String passportId) {
        Passport passport = new Passport();
        passport.setPassportId(passportId);
        return super.getRow(passport);
    }

    @Override
    public boolean updatePassport(Passport passport) {
        return super.updateRow(passport);
    }

    @Override
    public boolean createPassport(Passport passport) {
        return super.putRow(passport);
    }

    @Override
    public List<Passport> getByHandleName(String handleName) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .should(QueryBuilders.term("handle_name", handleName))
                .should(QueryBuilders.term("lower_handle_name", handleName.toLowerCase()));
        return search(boolQuery.build(), new Sort(List.of()), 0, 1, Passport.SEARCH_INDEX_PASSPORT);
    }

    @Override
    public TokenPage<Passport> listAllPassport(Integer limit, String nextToken) {
        MatchAllQuery.Builder builder = QueryBuilders.matchAll();
        Sort sort = new Sort(List.of(new FieldSort("create_time", SortOrder.DESC)));
        return pageSearchQuery(builder.build(), sort, nextToken, limit, Passport.SEARCH_INDEX_PASSPORT);
    }

    @Override
    public Passport getByInviteCode(String inviteCode) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("referral_code", inviteCode));
        return searchOne(boolQuery.build(), Passport.SEARCH_INDEX_PASSPORT);
    }

    @Override
    public Boolean save(Passport passport) {
        if(passport.getPassportId() == null) {
            passport.setPassportId(idUtils.nextId());
        }
        return putRow(passport);
    }
}
