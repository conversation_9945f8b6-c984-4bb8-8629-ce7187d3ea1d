package com.drex.customer.service.config;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/4/28 17:05
 * @description:
 */
@Configuration
public class CustomerConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean("okHttpClient")
    public OkHttpClient okHttpClient(CustomerProperties customerProperties){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        client.dispatcher().setMaxRequests(customerProperties.getHttpPoolSize());
        client.dispatcher().setMaxRequestsPerHost(customerProperties.getHttpPoolSize());
        return client;
    }
}
