package com.drex.customer.service.business.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.InviteService;
import com.drex.customer.service.message.DelayedMessageService;
import com.drex.model.CacheKey;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:15
 * @description:
 */
@Service
public class CustomerBindServiceImpl implements CustomerBindService {
    @Resource
    private CustomerBindBuilder customerBindBuilder;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private DelayedMessageService delayedMessageService;
    @Resource
    private InviteService inviteService;


    private final static String eventCodePrefix = "connect_";

    @Override
    public boolean insert(CustomerBind customerBind) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerBind.getCustomerId());
        redisTemplate.delete(cacheKey);
        if (customerBindBuilder.insert(customerBind)) {
            //触发邀请逻辑
            List<CustomerBind> customerBinds = customerBindBuilder.findByCustomerId(customerBind.getCustomerId());
            if (customerBinds == null) {
                customerBinds = List.of();
            }
            //只有google和email校验socialEmail,其他平台校验socialUserId不为空
            customerBinds = customerBinds.stream().filter(bind -> {
                String platform = bind.getSocialPlatform();
                if (platform == null) {
                    return false;
                }
                if (WalletConstant.PlatformEnum.Email.name().equalsIgnoreCase(platform) ||
                        WalletConstant.PlatformEnum.Google.name().equalsIgnoreCase(platform)) {
                    return StringUtils.isNotBlank(bind.getSocialEmail());
                }
                return StringUtils.isNotBlank(bind.getSocialUserId());
            }).toList();
            if(customerBinds.size() >= 2){
                inviteService.updateReferrerStatusToValid(customerBind.getCustomerId());
            }
//            //发送延迟mq消息异步触发verify任务
//            String eventCode = eventCodePrefix + customerBind.getSocialPlatform().toLowerCase();
//            Map<String, Object> body = new HashMap<>();
//            body.put("appId", "trex");
//            ActivityEventMessage msg = ActivityEventMessage.builder()
//                    .eventCode(eventCode)
//                    .customerId(customerBind.getCustomerId())
//                    .eventTime(System.currentTimeMillis())
//                    .body(body)
//                    .build();
//
//            // 使用延迟消息服务发送消息
//            delayedMessageService.sendDelayedActivityEvent(msg);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean delete(CustomerBind customerBind) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerBind.getCustomerId());
        redisTemplate.delete(cacheKey);
        return customerBindBuilder.delete(customerBind);
    }

    @Override
    public CustomerBind findByCustomerId(String customerId, String socialPlatform) {
        return customerBindBuilder.findByCustomerId(customerId, socialPlatform);
    }

    @Override
    public List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerId);
        String cache = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(cache)) {
            return JSON.parseArray(cache, CustomerBind.class).stream().filter(customerBind -> socialPlatforms.contains(customerBind.getSocialPlatform())).toList();
        }
        List<CustomerBind> customerBinds = customerBindBuilder.findByCustomerId(customerId, socialPlatforms);
        if (!customerBinds.isEmpty()) {
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(customerBinds), 3, TimeUnit.DAYS);
        }
        return customerBinds;
    }


    @Override
    public CustomerBind findBySocialInfo(CustomerBindQuery query) {
        return customerBindBuilder.findBySocialInfo(query);
    }

    @Override
    public boolean reservePrivacyAuth(String customerId, String socialPlatform) {
        CustomerBind customerBind = customerBindBuilder.findByCustomerId(customerId, socialPlatform);
        if (customerBind != null) {
            customerBind.setPrivacyAuth(!customerBind.getPrivacyAuth());
        } else {
            customerBind = new CustomerBind();
            customerBind.setCustomerId(customerId);
            customerBind.setSocialPlatform(socialPlatform);
            customerBind.setPrivacyAuth(true);
            customerBind.setCreated(System.currentTimeMillis());
        }
        return insert(customerBind);
    }
}
