package com.drex.customer.service.business.impl;

import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.service.business.ThirdWebService;
import com.drex.customer.service.config.CustomerProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ThirdWebServiceImpl implements ThirdWebService {

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private CustomerProperties customerProperties;

    @Override
    public ThirdWebUserAccount getThirdWebUserAccount(String walletAddress, WalletConstant.PlatformEnum subConnectProvider) {
        try {
            Request thidwebrequest = new Request.Builder()
                    .url("https://in-app-wallet.thirdweb.com/api/2023-11-30/embedded-wallet/user-details?queryBy=walletAddress&walletAddress="+walletAddress)
                    .get()
                    .addHeader("x-secret-key", customerProperties.getThirdWebSecretKey())
                    .addHeader("x-client-id", customerProperties.getThirdWebClientId())
                    .addHeader("Content-Type", "application/json")
                    .build();
            okhttp3.Response response = okHttpClient.newCall(thidwebrequest).execute();
            String responseBody = response.body().string();
            log.info("thirdweb response:{}", responseBody);
            com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSON.parseArray(responseBody);
            if (jsonArray != null && !jsonArray.isEmpty()) {
                ThirdWebUserAccount thirdWebUserAccount = new ThirdWebUserAccount();
                for (int i = 0; i < jsonArray.size(); i++) {

                    com.alibaba.fastjson.JSONObject jsonObject = jsonArray.getJSONObject(i);
                    thirdWebUserAccount.setAddress(jsonObject.getString("walletAddress"));

                    com.alibaba.fastjson.JSONArray linkedAccounts = jsonObject.getJSONArray("linkedAccounts");
                    if (linkedAccounts != null) {
                        for (int j = 0; j < linkedAccounts.size(); j++) {
                            com.alibaba.fastjson.JSONObject linkedAccount = linkedAccounts.getJSONObject(j);

                            // 匹配社媒类型
                            String type = linkedAccount.getString("type");
                            if (StringUtils.isNotBlank(type) && type.equalsIgnoreCase(subConnectProvider.getCode())) {
                                thirdWebUserAccount.setType(linkedAccount.getString("type"));

                                com.alibaba.fastjson.JSONObject details = linkedAccount.getJSONObject("details");
                                thirdWebUserAccount.setDetails(details.toJSONString());

                                thirdWebUserAccount.setId(details.getString("id"));
                                thirdWebUserAccount.setUsername(details.getString("name"));
                                thirdWebUserAccount.setEmail(jsonObject.getString("email"));
                                thirdWebUserAccount.setDetails(details.toJSONString());
                                return thirdWebUserAccount;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getThirdWebUserAccount error", e);
        }
        return null;
    }

}


