package com.drex.customer.service.remote.impl;

import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.cache.PassportCacheService;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

@Slf4j
@DubboService
public class RemotePassportServiceImpl implements RemotePassportService {

    @Resource
    private PassportService passportService;
    @Resource
    private PassportCacheService passportCacheService;

    @Override
    public PassportDTO getPassportById(String passportId) {
        return passportCacheService.get(passportId);
    }

    @Override
    public Response<PassportDTO> getByHandleName(String handleName) {
        PassportDTO passportDTO = passportService.getByHandleName(handleName);
        return Response.success(passportDTO);
    }

    @Override
    public Response<Boolean> updateHandleName(String passportId, String handleName) {
        passportCacheService.delete(passportId);
        Boolean updated = passportService.updateHandleName(passportId, handleName);
        return Response.success(updated);
    }

    @Override
    public Response<Boolean> updateAvatar(String passportId, String avatar) {
        passportCacheService.delete(passportId);
        Boolean updated = passportService.updateAvatar(passportId, avatar);
        return Response.success(updated);
    }

    @Override
    public Response<Boolean> bindWallet(BindWalletRequest request) {
        try {
            if (StringUtils.isEmpty(request.getPassportId()) || StringUtils.isEmpty(request.getWalletAddress())) {
                return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), "passportId and walletAddress are required");
            }
            PassportDTO passportDTO = passportService.getPassportByWalletAddress(request.getWalletAddress(), request.getSubConnectProvider());
            if(passportDTO != null){
                return Response.error(ErrorCode.WALLET_ALREADY_BIND.getCode(), "customerId and walletAddress has bound");
            }
            passportCacheService.delete(request.getPassportId());
            // Update connect_wallet
            boolean success = passportService.bindWalletAddress(request);
            return Response.success(success);
        } catch (Exception e) {
            log.error("bindWallet error", e);
            return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), ErrorCode.UNKNOWN_ERROR.getMessage());
        }
    }

    @Override
    public Response<Boolean> unbindWallet(UnbindWalletRequest request) {
        try {
            if (StringUtils.isEmpty(request.getPassportId()) || StringUtils.isEmpty(request.getWalletAddress())) {
                return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), "passportId and walletAddress are required");
            }

            passportCacheService.delete(request.getPassportId());
            boolean success = passportService.unbindWalletAddress(request);
            return Response.success(success);
        } catch (Exception e) {
            log.error("unbindWallet error", e);
            return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), ErrorCode.UNKNOWN_ERROR.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateKycLevel(String customerId, String kycLevel) {
        //todo update
        return null;
    }

    @Override
    public Response<List<PassportConnectDTO>> getPassportConnect(String passportId) {
        return Response.success(passportService.getPassportConnect(passportId));
    }
}