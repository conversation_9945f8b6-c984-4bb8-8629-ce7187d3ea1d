package com.drex.customer.service.business;

import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.dal.tablestore.model.CustomerBind;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:15
 * @description:
 */
public interface CustomerBindService {
    boolean insert(CustomerBind customerBind);

    boolean delete(CustomerBind customerBind);

    CustomerBind findByCustomerId(String customerId, String socialPlatform);

    List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms);

    /**
     * 根据社交平台信息查找已绑定的客户记录
     *
     * @param query 查询参数
     * @return 匹配的客户绑定记录，如果没有找到则返回null
     */
    CustomerBind findBySocialInfo(CustomerBindQuery query);

    boolean reservePrivacyAuth(String customerId, String socialPlatform);
}
