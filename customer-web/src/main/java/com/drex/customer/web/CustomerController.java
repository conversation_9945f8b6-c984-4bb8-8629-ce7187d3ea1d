package com.drex.customer.web;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import co.evg.achievement.api.BadgeAssetsStrategy;
import co.evg.achievement.api.NftDTO;
import co.evg.achievement.api.NftStatusEnum;
import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.request.CustomerCodeGenerateRequest;
import com.drex.customer.dal.tablestore.builder.PassportBuilder;
import com.drex.customer.dal.tablestore.model.Passport;
import com.drex.customer.service.code.client.CustomerCodeServiceClient;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.reference.IAchievementReference;
import com.drex.customer.service.reference.impl.model.anchor.BadgeSeriesResult;
import com.drex.customer.service.util.HttpPoolUtil;
import com.drex.customer.web.config.UploadAliyunOssUtil;
import com.drex.model.CustomerException;
import com.github.hui.quick.plugin.qrcode.wrapper.QrCodeGenWrapper;
import com.github.hui.quick.plugin.qrcode.wrapper.QrCodeOptions;
import com.google.zxing.WriterException;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.kikitrade.framework.common.model.TokenPage;
import jakarta.annotation.Resource;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:18
 * @description:
 */
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Resource
    private CustomerCodeServiceClient customerCodeServiceClient;
    @Resource
    private UploadAliyunOssUtil uploadAliyunOssUtil;
    @Resource
    private CustomerProperties customerProperties;
    @Resource
    private IAchievementReference achievementReference;
    @Resource
    private EventClient eventClient;
    @Resource
    private PassportBuilder passportBuilder;

    /**
     *  curl -X POST \
        'http://localhost:8080/customer/generate-qr-codes?scene=test123&width=200&height=200&size=25&host=trex.xyz&num=5' \
        -H 'Content-Type: application/x-www-form-urlencoded'
     * @param scene
     * @param width
     * @param height
     * @param host
     * @param num
     * @return
     */
    @PostMapping("/generate-qr-codes")
    public ResponseEntity<String> generateQRCodes(
            @RequestParam String scene,
            @RequestParam(defaultValue = "83") int width,
            @RequestParam(defaultValue = "83") int height,
            @RequestParam(defaultValue = "trex.xyz") String host,
            @RequestParam(defaultValue = "1") int num,
            @RequestParam(defaultValue = "25") int size,
            @RequestParam(defaultValue = "#8CD683") String color
    ) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String dateStr = sdf.format(new Date());

        // Create temp directory for QR codes
        String tempDir = System.getProperty("java.io.tmpdir") + "qrcode/" + dateStr + "/";
        File mkdir = FileUtil.mkdir(tempDir);
        File mfile = downloadImage(customerProperties.getQrUrl());

        for(int i = 0; i < num; i++){
            CustomerCodeGenerateRequest request = new CustomerCodeGenerateRequest();
            request.setType(CustomerConstants.CodeType.SINGLE_USE);
            request.setScene(scene);
            request.setSource("backend");
            try{
                String code = customerCodeServiceClient.generateCode(request);
                File outFile = new File(tempDir + code + ".png");
                InputStream in = new FileInputStream(mfile);
                QrCode_Logo(String.format("%s/%s", host, code), "png", outFile, in, height, width, color);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        // Create zip file
        String zipFileName =  "/tmp/qrcodes_" + dateStr + ".zip";
        File zip = ZipUtil.zip(tempDir, zipFileName, true);
        uploadAliyunOssUtil.putObject("drex/trex-qr-codes", zipFileName, zip);

        mkdir.deleteOnExit();
        deleteDirectory(mkdir);
        zip.deleteOnExit();

        return ResponseEntity.ok(uploadAliyunOssUtil.getLocation("drex/trex-qr-codes", zipFileName));
    }

    /**
     * 用QrCode生成带logo的二维码
     * @param content
     * @param logo
     * @return
     */
    public static BufferedImage QrCode_Logo(String content, String format, File outFile, InputStream logo, int h, int w, String color) {
        BufferedImage image = null;
        try {
            image = QrCodeGenWrapper.of(content)
                    .setH(h)
                    .setW(w)
                    .setDrawPreColor(Color.decode(color))
                    .setDrawStyle(QrCodeOptions.DrawStyle.RECT)
                    .setLogo(logo)
                    .setLogoRate(8) //设置比例
                    .asBufferedImage();
            ImageIO.write(image, format, outFile);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (WriterException e) {
            e.printStackTrace();
        }
        return image;
    }

    /**
     * Generate QR code with center logo
     * @param content QR code content
     * @param logoFile Logo file to place in center
     * @param outputFile Output file
     * @throws IOException If file operations fail
     */
    public static void generateQrWithLogo(String content, File logoFile, File outputFile, int width, int height, int size) throws IOException {
        // QR code configuration
        QrConfig config = new QrConfig(width, height);
        // Set margin to 0 for minimal border
        config.setMargin(1);
        config.setForeColor(Color.decode("#8CD683"));
        config.setErrorCorrection(ErrorCorrectionLevel.M);
        config.setRatio(10);

        // Generate base QR code
        BufferedImage qrImage = QrCodeUtil.generate(content, config);

        // Load and resize logo
        BufferedImage logo = ImageIO.read(logoFile);
        Image resizedLogo = ImgUtil.scale(logo, size, size);

        // Draw logo on QR code
        Graphics2D g2d = qrImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.drawImage(resizedLogo, (width - size) / 2, (height - size) / 2, null);
        g2d.dispose();

        // Save final image
        ImageIO.write(qrImage, "png", outputFile);
    }

    private File downloadImage(String imageUrl) {
        try {
            Request request = new Request.Builder().url(imageUrl).build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String outputPath = "/tmp/qr.png";
                return FileUtil.writeBytes(response.body().bytes(), outputPath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * curl -X POST \
        'http://localhost:8080/customer/send-badge?customerId=1'
     * @return
     */
    @PostMapping("/send-badge")
    public ResponseEntity<String> sendBadge(@RequestParam String customerId) {
        pushBetOrderEvent(customerId);
        return ResponseEntity.ok("success");
    }

    public void pushBetOrderEvent(String customerId) {
        try {
            JSONObject body = new JSONObject();
            body.put("customerId", customerId);
            body.put("contentId", customerId + "_claimable_badge");
            body.put("series", "TrexJourney");

            EventDTO event = EventDTO.builder()
                    .name("claimable_badge")
                    .time(System.currentTimeMillis())
                    .customerId(customerId)
                    .globalUid(body.getString("contentId"))
                    .source(eventClient.getDefaultSource())
                    .body(body)
                    .build();
            eventClient.asyncPush(event);
        } catch (Exception e) {
        }
    }

    @PostMapping("/anchor/series/badge")
    public ResponseEntity<String> getNftSeries(@RequestParam String series) {
        try {
            BadgeSeriesResult.BadgeSeriesDetail nftSeries = achievementReference.getNftSeries(series);
            return ResponseEntity.ok(JSONObject.toJSONString(nftSeries));
        } catch (CustomerException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/anchor/series/ntfs")
    public ResponseEntity<String> getNfts(@RequestParam String customerId,
                                          @RequestParam NftStatusEnum statusEnum,
                                          @RequestParam BadgeAssetsStrategy strategy) {
        List<NftDTO> nftSeries = achievementReference.badgeAssets(customerId, statusEnum, strategy);
        return ResponseEntity.ok(JSONObject.toJSONString(nftSeries));
    }


    public static void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            // 先删除子文件和子目录
            File[] files = directory.listFiles();
            if (files!= null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        // 再删除当前目录
        directory.delete();
    }

    @PostMapping("/anchor/badge")
    public ResponseEntity<String> anchorBadge(@RequestParam(required = false) String passportId) {

        if (StringUtils.isNotBlank(passportId)) {
            Passport byPassportId = passportBuilder.getByPassportId(passportId);

            // 为指定用户发送早鸟徽章
            Map<String, String> body = new HashMap<>();
            body.put("progressValue", byPassportId.getCreatedAt().getTime() + "");
            sendBadgeEvent(byPassportId.getPassportId(), CustomerConstants.SCENE_TREX_OG, CustomerConstants.SERIES_TREX_JOURNEY, body);
            return ResponseEntity.ok("success");
        }

        String nextToken = null;
        int totalProcessed = 0;

        do {
            // 查询一页用户数据
            TokenPage<Passport> passportTokenPage = passportBuilder.listAllPassport(100, nextToken);
            List<Passport> passports = passportTokenPage.getRows();

            if (passports != null && !passports.isEmpty()) {
                // 处理当前页的用户
                for (Passport passport : passports) {
                    try {
                        // 为每个用户发送早鸟徽章
                        Map<String, String> body = new HashMap<>();
                        body.put("progressValue", passport.getCreatedAt().getTime() + "");
                        sendBadgeEvent(passport.getPassportId(), CustomerConstants.SCENE_TREX_OG, CustomerConstants.SERIES_TREX_JOURNEY, body);
                        totalProcessed++;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            // 获取下一页的token
            nextToken = passportTokenPage.getNextToken();

        } while (nextToken != null && !nextToken.isEmpty());

        return ResponseEntity.ok("Processed " + totalProcessed + " users");
    }

    private void sendBadgeEvent(String customerId, String scene, String series, Map<String, String> params) {
        JSONObject body = new JSONObject();
        if (params != null) {
            body.putAll(params);
        }
        body.put("customerId", customerId);
        body.put("contentId", customerId + "_" + scene);
        body.put("series", series);
        body.put("scene", scene);

        EventDTO event = EventDTO.builder()
                .name("claimable_badge")
                .time(System.currentTimeMillis())
                .customerId(customerId)
                .globalUid(body.getString("contentId"))
                .source(eventClient.getDefaultSource())
                .body(body)
                .build();
        eventClient.asyncPush(event);
    }
}
