package com.drex.customer.api.constants;

import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/4/25 17:43
 * @description:
 */
@Data
public class WalletConstant {

    @Getter
    public enum PassportConnectStatus {
        ACTIVE("active"),
        PENDING("pending"),
        DISABLED("disabled"),
        ;

        private final String code;

        PassportConnectStatus(String code) {
            this.code = code;
        }

        public static PassportConnectStatus getEnumByCode(String code) {
            return Arrays.stream(PassportConnectStatus.values()).filter(platformEnum -> platformEnum.code.equalsIgnoreCase(code)).findFirst().orElse(null);
        }
    }

    @Getter
    public enum WalletProviderEnum implements Serializable {
        ThirdWeb("thirdweb"),
        Reown("reown"),
        Wallet("wallet"),
        ;

        private final String code;

        WalletProviderEnum(String code) {
            this.code = code;
        }

        public static WalletProviderEnum getEnumByCode(String code) {
            return Arrays.stream(WalletProviderEnum.values()).filter(platformEnum -> platformEnum.code.equalsIgnoreCase(code)).findFirst().orElse(null);
        }
    }

    @Getter
    public enum PlatformEnum implements Serializable{
        Google("google"),
        Apple("apple"),
        Facebook("facebook"),
        Discord("discord"),
        Line("line"),
        X("x"),
        Coinbase("coinbase"),
        Farcaster("farcaster"),
        Telegram("telegram"),
        Github("github"),
        Twitch("twitch"),
        Steam("steam"),
        Guest("guest"),
        Backend("backend"),
        Email("email"),
        Phone("phone"),
        Passkey("passkey"),
        TikTok("tiktok"),
        Instagram("instagram"),
        Wallet("wallet"),
        ;

        private String code;

        PlatformEnum(String code) {
            this.code = code;
        }

        public static PlatformEnum getEnumByCode(String code) {
            return Arrays.stream(PlatformEnum.values()).filter(platformEnum -> platformEnum.code.equalsIgnoreCase(code)).findFirst().orElse(null);
        }
    }

    @Getter
    public enum WalletTypeEnum implements Serializable{
        EVM("EVM"),
        Solana("Solana"),
        SOCIAL("Social"),
        ;

        private final String code;

        WalletTypeEnum(String code) {
            this.code = code;
        }

        public static WalletTypeEnum getEnumByCode(String code) {
            return Arrays.stream(WalletTypeEnum.values()).filter(platformEnum -> platformEnum.code.equalsIgnoreCase(code)).findFirst().orElse(null);
        }
    }

    @Getter
    public enum ConnectTypeEnum implements Serializable{
        BIND("BIND"),
        KEY("KEY"),
        ;

        private final String code;

        ConnectTypeEnum(String code) {
            this.code = code;
        }

        public static ConnectTypeEnum getEnumByCode(String code) {
            return Arrays.stream(ConnectTypeEnum.values()).filter(platformEnum -> platformEnum.code.equalsIgnoreCase(code)).findFirst().orElse(null);
        }
    }

}
