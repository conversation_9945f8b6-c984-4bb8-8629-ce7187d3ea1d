package com.drex.customer.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

public interface CustomerConstants {

    String DEFAULT_INVITE = "DEFAULT_INVITE";
    String UNLIMITED = "UNLIMITED";
    String INTERVAL_LIMIT = "INTERVAL_LIMIT";
    String SINGLE_USE = "SINGLE_USE";

    String SERIES_TREX_JOURNEY = "TrexJourney";

    String SCENE_TREX_OG = "TREX_OG";

    @AllArgsConstructor
    @Getter
    enum CodeType {
        /**
         * CodeType 枚举
         */
        DEFAULT_INVITE(0, CustomerConstants.DEFAULT_INVITE),
        UNLIMITED(10, CustomerConstants.UNLIMITED),
        INTERVAL_LIMIT(20, CustomerConstants.INTERVAL_LIMIT),
        SINGLE_USE(30, CustomerConstants.SINGLE_USE),
        ;
        private int code;
        private String name;

        public static CodeType fromName(String name) {
            return Arrays.stream(CodeType.values()).filter(f -> f.getName().equals(name)).findFirst().orElse(DEFAULT_INVITE);
        }
        public static CodeType fromCode(int code) {
            return Arrays.stream(CodeType.values()).filter(f -> f.getCode() == code).findFirst().orElse(DEFAULT_INVITE);
        }
    }

    @AllArgsConstructor
    @Getter
    enum Status {
        /**
         * CodeType 枚举
         */
        //有效
        EFFECTIVE(1, "effective"),
        //已核销
        WRITTEN_OFF(10, "writtenOff"),
        ;
        private int code;
        private String name;
    }

    @AllArgsConstructor
    @Getter
    enum LimitCycle {
        /**
         * limit cycle 枚举
         */
        UNLIMITED(0, "UNLIMITED"),
        YEARLY(1, "YEARLY"),
        MONTHLY(2, "MONTHLY"),
        WEEKLY(3, "WEEKLY"),
        DAILY(4, "DAILY"),
        ;
        private int sort;

        private String name;
        public static LimitCycle fromName(String name) {
            return Arrays.stream(LimitCycle.values()).filter(f -> f.getName().equals(name)).findFirst().orElse(null);
        }
    }

    @AllArgsConstructor
    @Getter
    enum SceneEnum {
        register,
        ;
    }

    @AllArgsConstructor
    @Getter
    enum RefferalStatus {

        INVALID("INVALID"),
        VALID("VALID"),
        ;
        private String key;
    }
}
